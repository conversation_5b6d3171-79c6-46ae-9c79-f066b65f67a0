#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG病例信息提取系统

此脚本用于从已分类的VNG病例文档中提取完整的患者信息和检查数据，
包括患者基本信息、VNG检查项目结果、位置试验结果和医生诊断等。

使用方法:
    python vng_data_extractor.py [--source-dir SOURCE_DIR] [--output-dir OUTPUT_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import csv
import shutil
import argparse
import traceback
from pathlib import Path
from datetime import datetime
import docx
import olefile


class VNGDataExtractor:
    """VNG病例数据提取器"""
    
    def __init__(self, source_dir, output_dir, debug=False):
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.debug = debug
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'failed_files', 'cannot_extract'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'failed_files', 'format_error'), exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'success_count': 0,
            'failed_count': 0,
            'cannot_extract': 0,
            'format_error': 0,
            'year_stats': {}
        }
        
        # 日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(output_dir, f'processing_log_{timestamp}.txt')
        self.csv_file = os.path.join(output_dir, f'vng_patient_data_{timestamp}.csv')

        # 乱码清理模式
        self.corruption_patterns = [
            r'ࠀ[ࠀ-࿿]+', r'[ᄀ-ᇿ]+', r'[䀀-䶿]+', r'[一-龯]{0,2}[ࠀ-࿿][一-龯]{0,2}',
            r'[ᘀ-ᘿ]+', r'[㸀-㿿]+', r'[Ā-ſ]{5,}', r'[Ĩ-ſ]{3,}', r'[ÿ]{2,}',
            r'[搒摧桤愀]{3,}', r'[혈鐇鐏鐐]{2,}', r'[ӿ]{3,}', r'[Ĥ]{2,}',
            r'[ᡊ伀儀帀]{2,}', r'[瀁桰漀]{2,}'
        ]
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        if self.debug:
            print(log_message)
            
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def extract_text_from_file(self, file_path):
        """从文件中提取文本内容，包括颜色标记的选项"""
        file_ext = os.path.splitext(file_path)[1].lower()
        full_text = ""
        colored_selections = {}  # 存储颜色标记的选项

        try:
            if file_ext == '.docx':
                doc = docx.Document(file_path)

                # 提取段落文本，同时检查颜色标记
                for paragraph in doc.paragraphs:
                    para_text = paragraph.text
                    full_text += para_text + "\n"

                    # 检查颜色标记的选项
                    colored_options = self.extract_colored_options(paragraph)
                    if colored_options:
                        colored_selections.update(colored_options)

                # 提取表格文本
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            full_text += cell.text + " "
                    full_text += "\n"
                    
            elif file_ext in ['.doc', '.wps']:
                # 首先尝试用docx方式读取（某些WPS文件可能兼容）
                try:
                    doc = docx.Document(file_path)
                    # 提取段落文本，同时检查颜色标记
                    for paragraph in doc.paragraphs:
                        para_text = paragraph.text
                        full_text += para_text + "\n"

                        # 检查颜色标记的选项
                        colored_options = self.extract_colored_options(paragraph)
                        if colored_options:
                            colored_selections.update(colored_options)

                    # 提取表格文本
                    for table in doc.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                full_text += cell.text + " "
                        full_text += "\n"

                except Exception:
                    # 如果docx方式失败，使用olefile方式
                    if olefile.isOleFile(file_path):
                        ole = olefile.OleFileIO(file_path)

                        # 尝试读取文档内容流，使用更多编码方式
                        priority_streams = ['WordDocument', '1Table', '0Table', 'Data', 'CompObj']

                        for stream_name in priority_streams:
                            try:
                                if ole.exists(stream_name):
                                    stream_data = ole.openstream(stream_name).read()

                                    # 尝试多种编码方式和文本清理
                                    best_text = ""
                                    best_score = 0

                                    for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-16le', 'utf-16be', 'latin1']:
                                        try:
                                            text_data = stream_data.decode(encoding, errors='ignore')

                                            # 改进的文本清理
                                            clean_text = self.clean_extracted_text(text_data)

                                            # 评分：包含的关键词越多分数越高
                                            score = sum(1 for keyword in ['姓名', '性别', '年龄', '检查日期', 'VNG', '视频眼震', '定标试验', '自发性眼震']
                                                       if keyword in clean_text)

                                            if score > best_score:
                                                best_score = score
                                                best_text = clean_text

                                        except Exception:
                                            continue

                                    if best_text and best_score > 0:
                                        full_text += best_text

                                    if full_text.strip():
                                        break
                            except Exception:
                                continue
                        ole.close()
                    
        except Exception as e:
            self.log(f"提取文本失败 {file_path}: {e}")
            return ""
            
        # 将颜色标记的选项合并到文本中
        if colored_selections:
            full_text += "\n=== 颜色标记选项 ===\n"
            for key, value in colored_selections.items():
                full_text += f"{key}: {value}\n"

        return full_text

    def clean_extracted_text(self, raw_text):
        """清理从WPS/DOC文件中提取的文本"""
        import re

        # 移除控制字符，但保留中文字符和基本标点
        cleaned = ""
        for char in raw_text:
            # 保留中文字符、英文字母、数字、基本标点和空白字符
            if (char.isalnum() or
                char in '，。：；！？（）【】""''、/\\-_=+<>%°/s．' or
                char.isspace() or
                '\u4e00' <= char <= '\u9fff'):  # 中文字符范围
                cleaned += char
            elif ord(char) < 32:  # 跳过控制字符
                continue

        # 清理多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned)

        # 移除明显的乱码模式
        cleaned = re.sub(r'[^\u4e00-\u9fff\w\s，。：；！？（）【】""''、/\\-_=+<>%°．]+', ' ', cleaned)

        # 移除重复的特殊字符序列
        cleaned = re.sub(r'([^\u4e00-\u9fff\w\s])\1{3,}', r'\1', cleaned)

        # 移除过短的垃圾片段
        lines = cleaned.split('\n')
        valid_lines = []
        for line in lines:
            line = line.strip()
            # 只保留包含中文或有意义内容的行
            if (len(line) > 2 and
                (any('\u4e00' <= char <= '\u9fff' for char in line) or  # 包含中文
                 any(keyword in line for keyword in ['VNG', 'Roll', 'Test', 'Dix']))):  # 包含关键词
                valid_lines.append(line)

        return '\n'.join(valid_lines)

    def clean_examiner_field(self, examiner_text):
        """清理检查者字段中的乱码"""
        import re

        # 只保留中文字符、英文字母和基本标点
        cleaned = ""
        for char in examiner_text:
            if (char.isalpha() or
                char in '，。：；（）' or
                char.isspace() or
                '\u4e00' <= char <= '\u9fff'):  # 中文字符范围
                cleaned += char

        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # 如果清理后太短或全是乱码，返回空字符串
        if len(cleaned) < 2 or not any('\u4e00' <= char <= '\u9fff' for char in cleaned):
            return ""

        return cleaned

    def extract_colored_options(self, paragraph):
        """提取段落中颜色标记的选项"""
        colored_options = {}
        para_text = paragraph.text

        # 查找包含'/'的选择项
        choice_patterns = re.findall(r'([^/\s]+/[^/\s]+(?:/[^/\s]+)*)', para_text)

        for choice_pattern in choice_patterns:
            # 分析这个选择项的颜色
            selected_option = self.analyze_choice_colors(paragraph, choice_pattern)
            if selected_option:
                # 找到选择项在文本中的上下文
                context_match = re.search(rf'([^。\n]*{re.escape(choice_pattern)}[^。\n]*)', para_text)
                if context_match:
                    context = context_match.group(1).strip()
                    colored_options[context] = selected_option

        return colored_options

    def analyze_choice_colors(self, paragraph, choice_text):
        """分析选择项中各选项的颜色，返回被选中的选项"""
        # 将选择项按'/'分割
        options = choice_text.split('/')
        if len(options) < 2:
            return None

        # 收集每个选项的颜色信息
        option_colors = {}

        for run in paragraph.runs:
            if not run.text.strip():
                continue

            run_color = self.get_text_color(run)

            # 检查这个run包含哪些选项
            for option in options:
                if option.strip() in run.text:
                    option_colors[option.strip()] = run_color

        # 分析颜色，找出被选中的选项
        selected_options = []
        black_options = []

        for option, color in option_colors.items():
            if color == 'colored':  # 蓝色或红色
                selected_options.append(option)
            elif color == 'black':  # 黑色
                black_options.append(option)

        # 如果只有一个选项是彩色的，其他是黑色，返回彩色的选项
        if len(selected_options) == 1 and len(black_options) >= 1:
            return selected_options[0]

        # 其他情况返回None，保持原始文本
        return None

    def get_text_color(self, run):
        """获取文本的颜色类型"""
        try:
            if run.font.color and run.font.color.rgb:
                rgb = run.font.color.rgb
                r, g, b = rgb.r, rgb.g, rgb.b

                # 判断颜色类型
                if r == 0 and g == 0 and b == 0:
                    return 'black'  # 黑色
                elif (r > 200 and g < 100 and b < 100) or \
                     (r < 100 and g < 100 and b > 200) or \
                     (r < 100 and g > 200 and b < 100):
                    return 'colored'  # 红色、蓝色或绿色
                else:
                    return 'colored'  # 其他非黑色
            else:
                # 没有颜色信息，默认认为是黑色
                return 'black'
        except:
            return 'black'

    def is_colored_text(self, run):
        """检查文本是否有颜色标记"""
        return self.get_text_color(run) == 'colored'

    def extract_colored_selections_from_file(self, file_path):
        """从docx文件中提取颜色选择信息"""
        try:
            import docx
            doc = docx.Document(file_path)
            colored_selections = {}

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # 提取这个段落中的颜色选择
                    para_selections = self.extract_colored_options(paragraph)
                    colored_selections.update(para_selections)

            return colored_selections
        except Exception as e:
            self.log(f"提取颜色信息时出错: {e}")
            return {}

    def clean_examiner_field(self, examiner_text):
        """清理检查者字段中的乱码"""
        if not examiner_text:
            return ""

        # 移除常见的乱码字符
        # 保留中文字符、英文字符、数字和常见标点
        cleaned = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,，。、；：！？()（）\[\]【】]', '', examiner_text)

        # 移除过长的连续乱码（超过10个字符的非中文字符串）
        cleaned = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf]{10,}', '', cleaned)

        # 清理多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # 如果清理后的内容太短或者全是乱码，返回空字符串
        if len(cleaned) < 2 or not re.search(r'[\u4e00-\u9fff]', cleaned):
            return ""

        return cleaned
    
    def extract_by_patterns(self, text, patterns):
        """使用多个正则表达式模式提取信息"""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return ""
    
    def extract_basic_info(self, text):
        """提取患者基本信息"""
        info = {}

        # 姓名 - 适配多种格式，包括WPS文件中的格式
        name_patterns = [
            r'姓名\s+([^\s]+)\s+性别',  # 姓名 苏敏聪 性别
            r'姓名\s*([^\s]+)\s*性别',  # 姓名苏敏聪性别
            r'姓名__([^\s]+)',  # 姓名__梁趣轩
            r'姓名[_：:]\s*([^\s_]+)',  # 姓名_张三 或 姓名：张三
            r'姓\s*名\s*[_：:]\s*([^\s_]+)',
            r'患者姓名\s*[：:]\s*([^\s]+)',
        ]
        info['姓名'] = self.extract_by_patterns(text, name_patterns)

        # 性别 - 适配WPS文件格式
        gender_patterns = [
            r'性别\s+([男女])\s+年龄',  # 性别 男 年龄
            r'性别\s*([男女])\s*年龄',  # 性别男年龄
            r'性别\s*[_：:]\s*([男女])',  # 性别  _女
            r'性\s*别\s*[_：:]\s*([男女])',
            r'患者性别\s*[：:]\s*([男女])',
        ]
        info['性别'] = self.extract_by_patterns(text, gender_patterns)

        # 年龄 - 适配下划线和岁字格式
        age_patterns = [
            r'年龄\s*[_：:]\s*(\d+)[岁]?',  # 年龄  72岁_
            r'年\s*龄\s*[_：:]\s*(\d+)[岁]?',
            r'(\d+)岁',  # 直接匹配数字+岁
        ]
        info['年龄'] = self.extract_by_patterns(text, age_patterns)

        # 检查日期
        date_patterns = [
            r'检\s*查\s*日\s*期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检查日期[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
        ]
        info['检查日期'] = self.extract_by_patterns(text, date_patterns)

        # 科别 - 适配WPS文件格式
        dept_patterns = [
            r'科别\s+([^\s]+)\s+门诊',  # 科别 神经一科专科 门诊
            r'科别\s*([^\s]+)\s*门诊',  # 科别神经一科专科门诊
            r'科别\s+([^门诊]+?)\s+专科',  # 科别 神经一科 专科
            r'科别\s+([^门诊]+?)\s+门诊',  # 科别 神经一科 门诊
            r'科别\s+([^门诊\s]+)',  # 科别 神经一科
            r'科别\s*[_：:]\s*([^_\s\n]{2,20})',  # 科别    _神经一科23床_
            r'科\s*别\s*[_：:]\s*([^_\s\n]{2,20})',
        ]
        info['科别'] = self.extract_by_patterns(text, dept_patterns)

        # 门诊/住院号 - 适配WPS文件格式
        id_patterns = [
            r'门诊[/／]住院号\s*([A-Za-z0-9]+)\s*编号',  # 门诊/住院号0000813951 编号
            r'门诊号[/／]住院号\s*([A-Za-z0-9]+)\s*编号',  # 门诊号/住院号0001389040编号
            r'门诊号\s*([A-Za-z0-9]+)\s*编号',  # 门诊号0001389040编号
            r'门诊[/／]住院号[_：:]\s*([A-Za-z0-9]+)',  # 门诊/住院号_ 200312 _
            r'住院号[_：:]\s*([A-Za-z0-9]+)',
            r'门诊号[_：:]\s*([A-Za-z0-9]+)',
        ]
        info['门诊住院号'] = self.extract_by_patterns(text, id_patterns)

        # 编号
        number_patterns = [
            r'编\s*号\s*[_：:]\s*([A-Za-z0-9]+)',
            r'编号[_：:]\s*([A-Za-z0-9]+)',
        ]
        info['编号'] = self.extract_by_patterns(text, number_patterns)

        return info
    
    def extract_examination_results(self, text, colored_selections=None):
        """提取检查结果 - 通过提取各项目之间的文本内容"""
        results = {}

        # 定义检查项目的顺序和可能的标题变体
        examination_items = [
            ('定标试验', [r'定标试验', r'1、定标试验', r'1\.定标试验']),
            ('自发性眼震', [r'自发性眼震', r'2、自发性眼震', r'2\.自发性眼震']),
            ('凝视试验', [r'凝视试验', r'3、凝视试验', r'3\.凝视试验']),
            ('平滑跟踪', [r'平滑跟踪', r'4、平滑跟踪', r'4\.平滑跟踪']),
            ('扫视试验', [r'扫视试验', r'5、扫视试验', r'5\.扫视试验']),
            ('视动性眼震', [r'视动性眼震', r'6、视动性眼震', r'6\.视动性眼震', r'7、视动性眼震', r'7\.视动性眼震']),
            ('高级扫视', [r'高级扫视', r'6、高级扫视', r'6\.高级扫视', r'7、高级扫视', r'7\.高级扫视']),
            ('位置试验', [r'位置试验', r'8、位置试验', r'8\.位置试验', r'9、位置试验', r'9\.位置试验']),
            ('摇头试验', [r'摇头试验', r'9、摇头试验', r'9\.摇头试验', r'10、摇头试验', r'10\.摇头试验']),
            ('印象', [r'印象', r'诊断印象', r'检查印象'])
        ]

        # 查找所有项目在文本中的位置
        found_items = []
        for item_name, patterns in examination_items:
            for pattern in patterns:
                # 查找项目标题的位置，支持有冒号和无冒号的格式
                match = re.search(rf'({pattern})\s*[：:]?\s*', text, re.IGNORECASE)
                if match:
                    start_pos = match.start()
                    title_end_pos = match.end()
                    found_items.append((item_name, start_pos, title_end_pos, match.group(0)))
                    break

        # 按位置排序
        found_items.sort(key=lambda x: x[1])

        # 提取各项目之间的内容
        for i, (item_name, start_pos, title_end_pos, title) in enumerate(found_items):
            # 确定内容的结束位置
            if i + 1 < len(found_items):
                # 下一个项目的开始位置
                next_start_pos = found_items[i + 1][1]

                # 在当前项目和下一个项目之间查找更精确的分界点
                content_section = text[title_end_pos:next_start_pos]

                # 查找下一个项目的编号模式（如"2、"、"3、"等）
                next_item_number_match = re.search(r'\s+(\d+)、', content_section)
                if next_item_number_match:
                    end_pos = title_end_pos + next_item_number_match.start()
                else:
                    end_pos = next_start_pos
            else:
                # 最后一个项目，提取到文档结束或检查者位置
                examiner_match = re.search(r'检查者[：:]', text[title_end_pos:])
                if examiner_match:
                    end_pos = title_end_pos + examiner_match.start()
                else:
                    end_pos = len(text)

            # 提取内容
            content = text[title_end_pos:end_pos].strip()

            # 特殊处理：如果是位置试验，需要检查是否包含摇头和印象
            if item_name == '位置试验':
                content, extracted_head_shake, extracted_impression = self.extract_head_shake_and_impression_from_position(content)

                # 如果从位置试验中提取到了摇头试验，且摇头试验列表中没有找到独立的摇头试验
                if extracted_head_shake and '摇头试验' not in [item[0] for item in found_items]:
                    results['摇头试验'] = extracted_head_shake

                # 如果从位置试验中提取到了印象，且印象列表中没有找到独立的印象
                if extracted_impression and '印象' not in [item[0] for item in found_items]:
                    results['印象'] = extracted_impression

            # 清理内容，传递颜色选择信息
            content = self.clean_examination_content(content, colored_selections)

            results[item_name] = content

        # 对于没有找到的项目，尝试使用原有的模式匹配方法作为备选
        self.fill_missing_examination_results(text, results)

        # 特殊处理印象：如果印象仍然为空，尝试更广泛的搜索
        if not results.get('印象') or results.get('印象').strip() == '' or str(results.get('印象')).lower() == 'nan':
            impression = self.extract_impression_fallback(text)
            if impression:
                results['印象'] = impression

        # 特殊处理摇头试验：如果摇头试验为空，尝试从文本中提取
        if not results.get('摇头试验') or results.get('摇头试验').strip() == '' or str(results.get('摇头试验')).lower() == 'nan':
            head_shake = self.extract_head_shake_fallback(text)
            if head_shake:
                results['摇头试验'] = head_shake

        return results

    def clean_examination_content(self, content, colored_selections=None):
        """清理检查内容"""
        if not content:
            return ""

        # 移除开头的冒号、空格等
        content = re.sub(r'^[：:\s]+', '', content)

        # 移除结尾的项目编号（如"2、"、"3、"等）
        content = re.sub(r'\s*\d+、\s*$', '', content)

        # 移除结尾的单独数字（下一个项目的序号）
        content = re.sub(r'\s+\d+\s*$', '', content)

        # 移除可能混入的下一个项目的开头部分
        # 查找常见的项目开头模式并截断
        next_item_patterns = [
            r'\s+(自发眼震|自发性眼震)\s+',
            r'\s+(凝视试验)\s+',
            r'\s+(平滑跟踪)\s+',
            r'\s+(扫视试验)\s+',
            r'\s+(视动性眼震)\s+',
            r'\s+(高级扫视)\s+',
            r'\s+(位置试验)\s+',
            r'\s+(摇头试验)\s+',
            r'\s+(印象)\s+'
        ]

        for pattern in next_item_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                content = content[:match.start()]
                break

        # 处理颜色选择项
        content = self.process_color_choices(content, colored_selections)

        # 移除结尾的多余空白
        content = content.strip()

        # 移除多余的换行符，用空格替代
        content = re.sub(r'\n+', ' ', content)

        # 移除多余的空格
        content = re.sub(r'\s+', ' ', content)

        # 移除明显的乱码或无意义字符
        content = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】%°/\\-_=+<>]+', '', content)

        return content.strip()

    def process_color_choices(self, content, colored_selections=None):
        """处理内容中的颜色选择项"""
        if not content or not colored_selections:
            return self.process_text_choices(content)

        # 首先检查是否有颜色信息可以应用
        for context, selected_option in colored_selections.items():
            # 如果内容包含这个上下文，尝试替换
            if context in content:
                # 找到选择项模式
                choice_match = re.search(r'([^/\s]+/[^/\s]+(?:/[^/\s]+)*)', context)
                if choice_match:
                    choice_text = choice_match.group(1)
                    # 在内容中替换选择项为选中的选项
                    content = content.replace(choice_text, selected_option)

        # 对剩余的选择项进行文本处理
        return self.process_text_choices(content)

    def process_text_choices(self, content):
        """处理文本中的选择项（没有颜色信息时的备用方法）"""
        if not content:
            return content

        # 定义常见的选择项模式
        choice_replacements = [
            (r'正常/异常', '正常'),
            (r'异常/正常', '正常'),
            (r'无/有', '无'),
            (r'有/无', '无'),
            (r'阴性/阳性', '阴性'),
            (r'阳性/阴性', '阴性'),
            (r'对称/不对称', '对称'),
            (r'不对称/对称', '对称'),
        ]

        # 应用替换规则
        for pattern, replacement in choice_replacements:
            content = re.sub(pattern, replacement, content)

        return content

    def apply_heuristic_rules(self, choice_text):
        """应用启发式规则来处理选择项"""
        options = [opt.strip() for opt in choice_text.split('/')]
        if len(options) < 2:
            return choice_text

        # 一些常见的启发式规则
        # 规则1：如果包含"正常"和"异常"，优先选择"正常"（在没有颜色信息时）
        if '正常' in options and '异常' in options:
            return '正常'

        # 规则2：如果包含"无"和"有"，优先选择"无"
        if '无' in options and '有' in options:
            return '无'

        # 规则3：如果包含"阴性"和"阳性"，优先选择"阴性"
        if '阴性' in options and '阳性' in options:
            return '阴性'

        # 规则4：如果包含"对称"和"不对称"，优先选择"对称"
        if '对称' in options and '不对称' in options:
            return '对称'

        # 其他情况保持原样
        return choice_text

    def extract_head_shake_and_impression_from_position(self, position_content):
        """从位置试验内容中提取摇头试验和印象"""
        head_shake = ""
        impression = ""
        cleaned_position = position_content

        # 首先查找印象的位置
        impression_match = re.search(r'印象[：:]?\s*(.+)', position_content)
        if impression_match:
            impression = impression_match.group(1).strip()
            # 从位置试验内容中移除印象部分
            cleaned_position = position_content[:impression_match.start()].strip()

        # 在剩余内容中查找摇头试验
        if cleaned_position:
            # 查找摇头试验内容（在位置试验中出现的摇头部分）
            head_shake_patterns = [
                r'摇头[：:]?\s*([^。]*。?)',
                r'摇头试验[：:]?\s*([^。]*。?)',
                r'10、摇头试验[：:]?\s*([^。]*。?)',
                r'9、摇头试验[：:]?\s*([^。]*。?)'
            ]

            for pattern in head_shake_patterns:
                head_shake_match = re.search(pattern, cleaned_position)
                if head_shake_match:
                    head_shake = head_shake_match.group(1).strip()
                    # 从位置试验内容中移除摇头部分
                    cleaned_position = cleaned_position[:head_shake_match.start()].strip()
                    break

        # 如果没有找到印象，但在原始内容中有印象，尝试更广泛的搜索
        if not impression:
            impression_patterns = [
                r'印象[：:]?\s*([^检查者]+?)(?:\s*检查者|$)',
                r'印象\s*([^检查者]+?)(?:\s*检查者|$)'
            ]
            for pattern in impression_patterns:
                match = re.search(pattern, position_content, re.DOTALL)
                if match:
                    impression = match.group(1).strip()
                    break

        return cleaned_position.strip(), head_shake, impression

    def extract_impression_fallback(self, text):
        """备用印象提取方法"""
        # 更广泛的印象搜索模式
        impression_patterns = [
            r'印象[：:]?\s*(.+?)(?:\s*检查者|$)',  # 印象到检查者或文档结束
            r'印象[：:]?\s*(.+?)(?:\s*\d+年\d+月\d+日|$)',  # 印象到日期或文档结束
            r'印象[：:]?\s*(.{10,200}?)(?:\s*检查者|$)',  # 印象内容长度在10-200字符之间
            r'印象([^检查者]{5,200}?)(?:\s*检查者|$)',  # 印象后直接跟内容，无冒号，最少5字符
            r'印象([^检查者]{5,200}?)(?:\s*ࠀ|$)',  # 印象后直接跟内容，到乱码字符
            r'印象([^检查者]{5,200}?)(?:\s*[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】%°/\\-_=+<>]|$)',  # 印象后直接跟内容，到非正常字符
            r'诊断印象[：:]?\s*(.+?)(?:\s*检查者|$)',
            r'检查印象[：:]?\s*(.+?)(?:\s*检查者|$)',
        ]

        for pattern in impression_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                impression = match.group(1).strip()
                # 清理印象内容
                impression = re.sub(r'\n+', ' ', impression)  # 换行替换为空格
                impression = re.sub(r'\s+', ' ', impression)  # 多个空格合并
                impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】%°/\\-_=+<>]+', '', impression)

                # 移除可能的乱码开头
                impression = re.sub(r'^[^\u4e00-\u9fff\w]+', '', impression)

                # 移除可能的乱码结尾
                impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】%°/\\-_=+<>]+$', '', impression)

                # 如果印象内容合理（长度大于3且小于500字符）
                if 3 < len(impression) < 500:
                    return impression

        return ""

    def extract_head_shake_fallback(self, text):
        """备用摇头试验提取方法"""
        head_shake_patterns = [
            r'摇头试验[：:]?\s*([^。]*。?)',
            r'摇头[：:]?\s*([^。]*。?)',
            r'10、摇头试验[：:]?\s*([^。]*。?)',
            r'9、摇头试验[：:]?\s*([^。]*。?)',
            r'摇头.*?(未见眼震|可诱发.*?眼震|未见明显|阴性|阳性|可见.*?眼震)',
        ]

        for pattern in head_shake_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                head_shake = match.group(1).strip()
                # 清理内容
                head_shake = re.sub(r'\n+', ' ', head_shake)
                head_shake = re.sub(r'\s+', ' ', head_shake)
                head_shake = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】%°/\\-_=+<>]+', '', head_shake)

                if 2 < len(head_shake) < 100:
                    return head_shake

        return ""

    def fill_missing_examination_results(self, text, results):
        """为缺失的检查项目填充内容，使用原有的模式匹配方法"""

        # 为每个可能缺失的项目尝试备用提取方法
        missing_items = {
            '定标试验': [
                r'定标试验[：:]?\s*([正异]常)',
                r'1、定标试验[：:]?\s*([正异]常)',
                r'定标[：:]?\s*([正异]常)'
            ],
            '自发性眼震': [
                r'自发性眼震[：:]?\s*([无有])',
                r'2、自发性眼震[：:]?\s*([无有])',
                r'自发眼震[：:]?\s*([无有])',
                r'自发.*?眼震[：:]?\s*([无有])'
            ],
            '凝视试验': [
                r'凝视试验[：:]?\s*([正异]常)',
                r'3、凝视试验[：:]?\s*([正异]常)',
                r'凝视[：:]?\s*([正异]常)'
            ],
            '平滑跟踪': [
                r'平滑跟踪[：:]?\s*([IⅠⅡⅢⅣ]+型)',
                r'4、平滑跟踪[：:]?\s*([IⅠⅡⅢⅣ]+型)',
                r'平滑[：:]?\s*([IⅠⅡⅢⅣ]+型)'
            ],
            '扫视试验': [
                r'扫视试验[：:]?\s*([正异]常)',
                r'5、扫视试验[：:]?\s*([正异]常)',
                r'扫视[：:]?\s*([正异]常)'
            ],
            '视动性眼震': [
                r'视动性眼震[：:]?\s*([正异]常)',
                r'视动.*?([对不]称)',
                r'左右.*?视动.*?([对不]称)'
            ]
        }

        for item_name, patterns in missing_items.items():
            if not results.get(item_name) or results.get(item_name).strip() == '':
                for pattern in patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        results[item_name] = match.group(1).strip()
                        break

        # 如果高级扫视为空，尝试提取错误率信息
        if not results.get('高级扫视') or results.get('高级扫视').strip() == '':
            advanced_saccade_info = []

            # 查找前扫视错误率
            front_saccade_match = re.search(r'前扫视.*?错误率(\d+%)', text)
            if front_saccade_match:
                advanced_saccade_info.append(f"前扫视方向错误率{front_saccade_match.group(1)}")

            # 查找总体错误率
            total_error_match = re.search(r'总体错误率(\d+%)', text)
            if total_error_match:
                advanced_saccade_info.append(f"总体错误率{total_error_match.group(1)}")

            # 查找反扫视信息
            anti_saccade_match = re.search(r'反扫视.*?(不能配合|无法配合|配合差)', text)
            if anti_saccade_match:
                advanced_saccade_info.append(f"反扫视{anti_saccade_match.group(1)}")

            if advanced_saccade_info:
                results['高级扫视'] = '，'.join(advanced_saccade_info)

        # 如果摇头试验为空，尝试提取相关信息
        if not results.get('摇头试验') or results.get('摇头试验').strip() == '':
            head_shake_patterns = [
                r'摇头试验[：:]?\s*([阴阳]性)',
                r'摇头[：:]?\s*([阴阳]性)',
                r'摇头.*?(未见眼震|可诱发.*?眼震|未见明显|阴性|阳性)'
            ]
            for pattern in head_shake_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    results['摇头试验'] = match.group(1).strip()
                    break
    
    def extract_position_tests_combined(self, text):
        """直接提取并合并位置试验结果 - 从位置试验部分提取完整内容"""

        # 首先尝试从examination_results中获取位置试验的完整内容
        # 如果examination_results中已经提取了位置试验内容，直接使用
        position_content = ""

        # 查找位置试验标题的位置
        position_patterns = [
            r'位置试验\s*[：:]?\s*',
            r'8、位置试验\s*[：:]?\s*',
            r'9、位置试验\s*[：:]?\s*'
        ]

        position_match = None
        for pattern in position_patterns:
            position_match = re.search(pattern, text, re.IGNORECASE)
            if position_match:
                break

        if position_match:
            # 找到位置试验标题，提取其后的内容
            start_pos = position_match.end()

            # 查找下一个主要项目的位置作为结束点
            next_items = [
                r'摇头试验\s*[：:]',
                r'印象\s*[：:]',
                r'检查者\s*[：:]',
                r'诊断印象\s*[：:]'
            ]

            end_pos = len(text)
            for pattern in next_items:
                next_match = re.search(pattern, text[start_pos:], re.IGNORECASE)
                if next_match:
                    end_pos = start_pos + next_match.start()
                    break

            # 提取内容
            position_content = text[start_pos:end_pos].strip()
            position_content = self.clean_examination_content(position_content)

        # 如果没有找到完整的位置试验内容，使用原有的细分提取方法
        if not position_content:
            position_info = []

            # Roll Test
            roll_patterns = [
                r'Roll\s*Test\s*([阴阳]性)',
                r'①Roll\s*Test\s*([阴阳]性)',
            ]
            roll_test = self.extract_by_patterns(text, roll_patterns)

            # 翻身试验
            turning_patterns = [
                r'翻身试验\s*([阴阳]性)',
                r'②翻身试验\s*([阴阳]性)',
            ]
            turning_test = self.extract_by_patterns(text, turning_patterns)

            # 处理Roll Test / 翻身试验
            if roll_test or turning_test:
                if roll_test and turning_test:
                    if roll_test.strip() == turning_test.strip():
                        position_info.append(f"①Roll Test/翻身试验: {roll_test.strip()}")
                    else:
                        position_info.append(f"①Roll Test: {roll_test.strip()}")
                        position_info.append(f"翻身试验: {turning_test.strip()}")
                elif roll_test:
                    position_info.append(f"①Roll Test: {roll_test.strip()}")
                elif turning_test:
                    position_info.append(f"①翻身试验: {turning_test.strip()}")

            # Dix-Hallpike左侧
            dix_left_patterns = [
                r'左侧悬头位\s*([阴阳]性)',
                r'Dix-Hallpike.*?左侧悬头位\s*([阴阳]性)',
            ]
            dix_left = self.extract_by_patterns(text, dix_left_patterns)

            # Dix-Hallpike右侧
            dix_right_patterns = [
                r'右侧悬头位\s*([阴阳]性)',
                r'Dix-Hallpike.*?右侧悬头位\s*([阴阳]性)',
            ]
            dix_right = self.extract_by_patterns(text, dix_right_patterns)

            # 处理Dix-Hallpike试验
            if dix_left or dix_right:
                dix_info = "②Dix-Hallpike"
                if dix_left:
                    dix_info += f" 左侧悬头位: {dix_left.strip()}"
                if dix_right:
                    if dix_left:
                        dix_info += f"; 右侧悬头位: {dix_right.strip()}"
                    else:
                        dix_info += f" 右侧悬头位: {dix_right.strip()}"
                position_info.append(dix_info)

            # 疲劳现象
            fatigue_patterns = [
                r'疲劳现象\s*([阴阳]性)',
            ]
            fatigue = self.extract_by_patterns(text, fatigue_patterns)
            if fatigue:
                position_info.append(f"疲劳现象: {fatigue.strip()}")

            # 合并信息
            position_content = "; ".join(position_info) if position_info else ""

        return position_content
    
    def extract_diagnosis(self, text):
        """提取诊断信息"""
        results = {}
        
        # 印象 - 提取完整的段落信息
        impression_patterns = [
            r'印象[：:]?\s*(.*?)(?:检查者|$)',  # 提取到检查者或文档结束，冒号可选
            r'印象[：:]?\s*([^。]*。)',         # 提取到第一个句号，冒号可选
            r'印象[：:]?\s*([^\n]*)',          # 提取到换行，冒号可选
            r'印象([^检查者]{5,200}?)(?:\s*检查者|$)',  # 印象后直接跟内容，无冒号
            r'印象([^检查者]{5,200}?)(?:\s*ࠀ|$)',  # 印象后直接跟内容，到乱码字符
            r'(所检眼震视图检查[^。]*。)',      # 特定格式的完整句子
        ]

        impression = self.extract_by_patterns(text, impression_patterns)

        # 如果没有找到，尝试更宽泛的搜索
        if not impression:
            # 查找包含"所检"的完整句子
            impression_match = re.search(r'(所检[^。]*。)', text)
            if impression_match:
                impression = impression_match.group(1)
            else:
                # 查找包含"未见明显异常"的句子
                impression_match = re.search(r'([^。]*未见明显异常[^。]*。)', text)
                if impression_match:
                    impression = impression_match.group(1)
                else:
                    # 查找其他可能的印象描述
                    impression_match = re.search(r'印象[：:]?\s*([^。]*)', text)
                    if impression_match:
                        impression = impression_match.group(1)

        if impression:
            # 清理印象内容
            impression = re.sub(r'\s+', ' ', impression).strip()
            impression = re.sub(r'[\n\r]+', ' ', impression)
            # 移除开头的冒号或空格
            impression = re.sub(r'^[：:\s]+', '', impression)
            # 确保以句号结尾
            if impression and not impression.endswith('。'):
                impression += '。'

        results['印象'] = impression.strip() if impression else ""
        
        # 不再提取检查者字段 - 按用户要求移除

        return results

    def extract_patient_data(self, file_path):
        """提取单个病例文件的完整数据"""
        filename = os.path.basename(file_path)

        # 不再提取年份 - 按用户要求移除

        # 提取文本内容
        text = self.extract_text_from_file(file_path)
        if not text:
            self.log(f"无法提取文本内容: {filename}")
            return None

        # 验证是否为VNG报告
        vng_keywords = ['视频眼震电图', 'VNG', '前庭功能检查']
        if not any(keyword in text for keyword in vng_keywords):
            self.log(f"非VNG报告: {filename}")
            return None

        # 提取各部分数据
        basic_info = self.extract_basic_info(text)

        # 获取颜色选择信息（如果是docx文件）
        colored_selections = None
        if filename.endswith('.docx'):
            colored_selections = self.extract_colored_selections_from_file(file_path)

        exam_results = self.extract_examination_results(text, colored_selections)
        diagnosis = self.extract_diagnosis(text)

        # 合并所有数据 - 按照指定顺序
        patient_data = {
            '文件名': filename,
        }

        patient_data.update(basic_info)
        patient_data.update(exam_results)

        # 位置试验已经在exam_results中提取了，如果为空则尝试备用方法
        if not patient_data.get('位置试验') or patient_data.get('位置试验').strip() == '':
            position_tests_combined = self.extract_position_tests_combined(text)
            if position_tests_combined:
                patient_data['位置试验'] = position_tests_combined

        patient_data.update(diagnosis)

        return patient_data

    def validate_data(self, data):
        """验证数据完整性 - 允许部分字段为空"""
        # 只要有姓名或者检查日期其中之一就认为是有效数据
        has_name = bool(data.get('姓名'))
        has_date = bool(data.get('检查日期'))

        # 至少要有姓名或检查日期
        if has_name or has_date:
            return True, []
        else:
            return False, ['姓名', '检查日期']

    def process_files(self):
        """处理所有文件"""
        self.log("开始处理VNG病例文件...")

        # 获取所有文件，排除undefined文件夹，并去重
        all_files = []
        seen_files = set()  # 用于去重的集合

        for root, dirs, files in os.walk(self.source_dir):
            # 跳过undefined文件夹
            if 'undefined' in root.lower():
                self.log(f"跳过undefined文件夹: {root}")
                continue

            for file in files:
                if file.endswith(('.docx', '.doc', '.wps')):
                    file_path = os.path.join(root, file)

                    # 使用文件名作为去重的键（因为同一个文件可能在不同目录中）
                    if file not in seen_files:
                        seen_files.add(file)
                        all_files.append(file_path)
                    else:
                        self.log(f"跳过重复文件: {file} (路径: {file_path})")

        self.stats['total_files'] = len(all_files)
        self.log(f"找到 {len(all_files)} 个文件需要处理")

        # 准备CSV文件 - 按照指定顺序生成列
        csv_headers = [
            '文件名', '姓名', '性别', '年龄', '检查日期', '科别', '门诊住院号', '编号',
            '定标试验', '自发性眼震', '凝视试验', '平滑跟踪', '扫视试验', '视动性眼震', '高级扫视',
            '位置试验', '摇头试验', '印象'
        ]

        successful_data = []

        # 处理每个文件
        for i, file_path in enumerate(all_files, 1):
            filename = os.path.basename(file_path)

            if i % 100 == 0 or i <= 10:
                self.log(f"处理进度: {i}/{len(all_files)} - {filename}")

            try:
                # 提取数据
                patient_data = self.extract_patient_data(file_path)

                if patient_data is None:
                    self.stats['cannot_extract'] += 1
                    # 移动到无法提取文件夹
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'cannot_extract', filename)
                    shutil.copy2(file_path, dest_path)
                    continue

                # 验证数据
                is_valid, missing_fields = self.validate_data(patient_data)

                if not is_valid:
                    self.log(f"数据不完整 {filename}: 缺失字段 {missing_fields}")
                    self.stats['format_error'] += 1
                    # 移动到格式错误文件夹
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'format_error', filename)
                    shutil.copy2(file_path, dest_path)
                    continue

                # 数据成功提取
                successful_data.append(patient_data)
                self.stats['success_count'] += 1

            except Exception as e:
                self.log(f"处理文件异常 {filename}: {e}")
                if self.debug:
                    self.log(traceback.format_exc())
                self.stats['failed_count'] += 1

                # 移动到格式错误文件夹
                try:
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'format_error', filename)
                    shutil.copy2(file_path, dest_path)
                except:
                    pass

        # 写入CSV文件
        if successful_data:
            self.write_csv(successful_data, csv_headers)
            self.log(f"成功提取 {len(successful_data)} 个病例数据")

            # 执行数据清理
            self.clean_and_finalize_data()

        # 生成统计报告
        self.generate_statistics_report()

        return successful_data

    def write_csv(self, data, headers):
        """写入CSV文件"""
        try:
            with open(self.csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=headers, quoting=csv.QUOTE_ALL)
                writer.writeheader()

                for row in data:
                    # 确保所有字段都存在
                    complete_row = {}
                    for header in headers:
                        complete_row[header] = row.get(header, '')
                    writer.writerow(complete_row)

            self.log(f"CSV文件已保存: {self.csv_file}")

        except Exception as e:
            self.log(f"写入CSV文件失败: {e}")

    def clean_name_field(self, name):
        """清理姓名字段"""
        if not name or str(name).strip() == '':
            return ''

        name = str(name).strip()
        name = re.sub(r'^[：:_\s]+', '', name)
        name = re.sub(r'[_\s]+$', '', name)
        name = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf·]', '', name)
        return name.strip()

    def clean_department_field(self, dept):
        """清理科别字段"""
        if not dept or str(dept).strip() == '':
            return ''

        dept = str(dept).strip()
        dept = re.sub(r'^[：:_\s]+', '', dept)
        dept = re.sub(r'[_\s]*\d+床[_\s]*$', '', dept)
        dept = re.sub(r'[_\s]+$', '', dept)
        return dept.strip()

    def clean_hospital_id(self, hospital_id):
        """清理住院号字段"""
        if not hospital_id or str(hospital_id).strip() == '':
            return ''

        hospital_id = str(hospital_id).strip()
        hospital_id = re.sub(r'[^A-Za-z0-9]', '', hospital_id)
        return hospital_id

    def standardize_date(self, date_str):
        """标准化日期格式"""
        if not date_str or str(date_str).strip() == '':
            return ''

        date_str = str(date_str).strip()

        # 处理各种日期格式
        date_patterns = [
            (r'(\d{1,2})/(\d{1,2})/(\d{4})', r'\3-\1-\2'),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),
        ]

        for pattern, replacement in date_patterns:
            if re.match(pattern, date_str):
                date_str = re.sub(pattern, replacement, date_str)
                break

        # 确保月份和日期是两位数
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            try:
                month = f"{int(month):02d}"
                day = f"{int(day):02d}"
                return f"{year}-{month}-{day}"
            except ValueError:
                return date_str

        return date_str

    def clean_impression_field(self, impression):
        """清理印象字段中的乱码"""
        if not impression or str(impression).strip() == '':
            return ''

        impression = str(impression)

        # 查找乱码模式的开始位置
        corruption_start = None
        for pattern in self.corruption_patterns:
            match = re.search(pattern, impression)
            if match:
                if corruption_start is None or match.start() < corruption_start:
                    corruption_start = match.start()

        # 如果找到乱码，截取乱码前的部分
        if corruption_start is not None:
            clean_end = corruption_start
            for i in range(corruption_start - 1, -1, -1):
                if impression[i] in '。，；、':
                    clean_end = i + 1
                    break
                elif impression[i] in '.,;':
                    clean_end = i + 1
                    break
            impression = impression[:clean_end]

        # 移除剩余的乱码
        for pattern in self.corruption_patterns:
            impression = re.sub(pattern, '', impression)

        # 清理多余的空白字符
        impression = re.sub(r'\s+', ' ', impression).strip()
        impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】]+$', '', impression)

        return impression.strip()



    def clean_and_finalize_data(self):
        """清理数据并生成最终CSV"""
        import pandas as pd

        self.log("开始数据清理...")

        # 读取刚生成的CSV文件
        df = pd.read_csv(self.csv_file)

        # 清理各个字段
        if '姓名' in df.columns:
            self.log("清理姓名字段...")
            df['姓名'] = df['姓名'].apply(self.clean_name_field)

        if '科别' in df.columns:
            self.log("清理科别字段...")
            df['科别'] = df['科别'].apply(self.clean_department_field)

        if '门诊住院号' in df.columns:
            self.log("清理住院号字段...")
            df['门诊住院号'] = df['门诊住院号'].apply(self.clean_hospital_id)

        if '检查日期' in df.columns:
            self.log("标准化检查日期...")
            df['检查日期'] = df['检查日期'].apply(self.standardize_date)

        if '印象' in df.columns:
            self.log("清理印象字段...")
            df['印象'] = df['印象'].apply(self.clean_impression_field)

        # 位置试验已经在提取时合并，无需再次处理

        # 保存最终清理后的数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        final_output_file = os.path.join(self.output_dir, f'vng_patient_data_final_{timestamp}.csv')
        df.to_csv(final_output_file, index=False, encoding='utf-8-sig')

        self.log(f"最终清理后的数据已保存到: {final_output_file}")

        # 打印统计信息
        self.log(f"最终数据统计: {len(df)} 行, {len(df.columns)} 列")

    def generate_statistics_report(self):
        """生成统计报告"""
        report_file = os.path.join(self.output_dir, 'statistics_report.txt')

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("VNG病例数据提取统计报告\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"总文件数: {self.stats['total_files']}\n")
                f.write(f"成功提取: {self.stats['success_count']}\n")
                f.write(f"无法提取: {self.stats['cannot_extract']}\n")
                f.write(f"格式错误: {self.stats['format_error']}\n")
                f.write(f"处理异常: {self.stats['failed_count']}\n\n")

                success_rate = (self.stats['success_count'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0
                f.write(f"成功率: {success_rate:.1f}%\n\n")

                if self.stats['year_stats']:
                    f.write("按年份统计:\n")
                    for year in sorted(self.stats['year_stats'].keys()):
                        count = self.stats['year_stats'][year]
                        f.write(f"  {year}年: {count} 个文件\n")

            self.log(f"统计报告已保存: {report_file}")

        except Exception as e:
            self.log(f"生成统计报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VNG病例信息提取系统')

    # 默认路径
    default_source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"
    default_output_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output"

    parser.add_argument('--source-dir', default=default_source_dir,
                       help=f'源文件目录路径 (默认: {default_source_dir})')
    parser.add_argument('--output-dir', default=default_output_dir,
                       help=f'输出目录路径 (默认: {default_output_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')

    args = parser.parse_args()

    print("VNG病例信息提取系统")
    print("=" * 50)
    print(f"源目录: {args.source_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"调试模式: {args.debug}")
    print("-" * 50)

    # 检查源目录
    if not os.path.exists(args.source_dir):
        print(f"错误: 源目录不存在 - {args.source_dir}")
        return 1

    # 创建提取器并处理
    extractor = VNGDataExtractor(args.source_dir, args.output_dir, args.debug)

    try:
        results = extractor.process_files()

        print("\n处理完成!")
        print(f"总文件数: {extractor.stats['total_files']}")
        print(f"成功提取: {extractor.stats['success_count']}")
        print(f"成功率: {extractor.stats['success_count']/extractor.stats['total_files']*100:.1f}%")
        print(f"输出文件: {extractor.csv_file}")

        return 0

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        if args.debug:
            print(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())

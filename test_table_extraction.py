#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/code')

def test_table_extraction():
    """测试表格纵向提取功能"""
    
    try:
        import docx
    except ImportError:
        print("需要安装python-docx库")
        return
    
    # 测试一些可能包含表格的docx文件
    test_files = [
        "380 黄美萍.docx",
        "285 吴海生.docx", 
        "488 颜佳祥.docx",
        "10528林厚桂.docx",
        "147 钟煜基.docx"
    ]
    
    for filename in test_files:
        # 查找文件
        test_file = None
        for root, dirs, files in os.walk("/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"):
            if filename in files:
                test_file = os.path.join(root, filename)
                break
        
        if not test_file:
            print(f"未找到文件: {filename}")
            continue
            
        print("=" * 80)
        print(f"测试文件: {filename}")
        print("=" * 80)
        
        try:
            doc = docx.Document(test_file)
            
            # 检查文档中的表格
            print(f"文档中共有 {len(doc.tables)} 个表格")
            
            for i, table in enumerate(doc.tables):
                print(f"\n--- 表格 {i+1} ---")
                
                # 检查是否为位置试验表格
                is_position_table = is_position_test_table(table)
                print(f"是否为位置试验表格: {is_position_table}")
                
                if is_position_table:
                    print("\n原始横向读取:")
                    print("-" * 40)
                    horizontal_text = extract_table_text_horizontal(table)
                    print(horizontal_text)
                    
                    print("\n纵向读取结果:")
                    print("-" * 40)
                    vertical_text = extract_position_test_table(table)
                    print(vertical_text)
                else:
                    # 显示表格的基本信息
                    print(f"表格大小: {len(table.rows)} 行 x {len(table.rows[0].cells) if table.rows else 0} 列")
                    if table.rows:
                        first_row_text = ' | '.join([cell.text.strip() for cell in table.rows[0].cells])
                        print(f"第一行内容: {first_row_text}")
                        
        except Exception as e:
            print(f"处理文件时出错: {e}")
        
        print("\n" + "="*80 + "\n")

def is_position_test_table(table):
    """判断是否为位置试验表格"""
    if len(table.rows) == 0:
        return False

    # 检查第一行是否包含Roll Test的标准列标题
    first_row_text = ' '.join([cell.text.strip() for cell in table.rows[0].cells])

    # Roll Test的关键词（包括变体）
    position_keywords = ['平卧位', '仰卧位', '左转头', '右转头']
    movement_keywords = ['左翻身', '右翻身', '左侧卧', '右侧卧']

    # 必须包含位置关键词和动作关键词
    has_position = any(keyword in first_row_text for keyword in position_keywords)
    has_movement = any(keyword in first_row_text for keyword in movement_keywords)

    # 或者至少包含3个Roll Test相关的关键词
    all_keywords = position_keywords + movement_keywords
    keyword_count = sum(1 for keyword in all_keywords if keyword in first_row_text)

    return (has_position and has_movement) or keyword_count >= 3

def extract_table_text_horizontal(table):
    """横向读取表格（当前的方式）"""
    text_parts = []
    for row in table.rows:
        row_text = ' '.join([cell.text.strip() for cell in row.cells if cell.text.strip()])
        if row_text:
            text_parts.append(row_text)
    return ' '.join(text_parts)

def extract_position_test_table(table):
    """纵向读取位置试验表格"""
    if len(table.rows) == 0:
        return ""
    
    # 获取表格的行数和列数
    rows = table.rows
    
    # 第一行通常是标题行（平卧位、左转头、右转头等）
    headers = [cell.text.strip() for cell in rows[0].cells]
    
    # 后续行是对应的检查结果
    results = []
    for col_index, header in enumerate(headers):
        if header:  # 跳过空标题
            column_content = [header]
            # 收集该列的所有内容
            for row_index in range(1, len(rows)):
                try:
                    cell_text = rows[row_index].cells[col_index].text.strip()
                    if cell_text:
                        column_content.append(cell_text)
                except IndexError:
                    break
            
            # 将该列的内容组合
            if len(column_content) > 1:
                results.append('\n'.join(column_content))
    
    return '\n\n'.join(results)

if __name__ == "__main__":
    print("开始测试表格纵向提取功能...")
    test_table_extraction()

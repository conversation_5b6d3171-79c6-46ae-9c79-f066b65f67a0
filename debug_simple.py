#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def test_simple():
    """简单测试"""
    
    test_case = "定标试验正常/异常"
    
    patterns = [
        r'[^/\s，。；：！？]+/[^/\s，。；：！？]+(?:/[^/\s，。；：！？]+)*',
        r'[^/]+/[^/]+',
        r'\w+/\w+',
        r'正常/异常',
        r'[^/]+/[^/]+'
    ]
    
    for i, pattern in enumerate(patterns):
        print(f"模式 {i+1}: {pattern}")
        matches = re.findall(pattern, test_case)
        print(f"输入: {test_case}")
        print(f"匹配: {matches}")
        print()

def test_process_function():
    """测试处理函数"""
    
    def apply_heuristic_rules(choice_text):
        """应用启发式规则来处理选择项"""
        print(f"处理选择项: {choice_text}")
        options = [opt.strip() for opt in choice_text.split('/')]
        print(f"选项: {options}")
        
        if len(options) < 2:
            return choice_text
        
        # 规则1：如果包含"正常"和"异常"，优先选择"正常"
        if '正常' in options and '异常' in options:
            print("应用规则: 正常/异常 -> 正常")
            return '正常'
        
        # 规则2：如果包含"无"和"有"，优先选择"无"
        if '无' in options and '有' in options:
            print("应用规则: 无/有 -> 无")
            return '无'
        
        # 规则3：如果包含"阴性"和"阳性"，优先选择"阴性"
        if '阴性' in options and '阳性' in options:
            print("应用规则: 阴性/阳性 -> 阴性")
            return '阴性'
        
        # 规则4：如果包含"对称"和"不对称"，优先选择"对称"
        if '对称' in options and '不对称' in options:
            print("应用规则: 对称/不对称 -> 对称")
            return '对称'
        
        print("没有匹配的规则，保持原样")
        return choice_text
    
    def process_text_choices(content):
        """处理文本中的选择项"""
        if not content:
            return content
        
        print(f"处理内容: {content}")
        
        # 查找所有包含'/'的选择项
        pattern = r'[^/\s，。；：！？]+/[^/\s，。；：！？]+(?:/[^/\s，。；：！？]+)*'
        choice_patterns = re.findall(pattern, content)
        
        print(f"找到的选择项: {choice_patterns}")
        
        for choice_pattern in choice_patterns:
            # 对于没有颜色信息的选择项，应用一些启发式规则
            processed_choice = apply_heuristic_rules(choice_pattern)
            if processed_choice and processed_choice != choice_pattern:
                print(f"替换: {choice_pattern} -> {processed_choice}")
                content = content.replace(choice_pattern, processed_choice)
        
        return content
    
    test_cases = [
        "定标试验正常/异常",
        "自发性眼震无/有",
        "Roll Test阴性/阳性"
    ]
    
    for test_case in test_cases:
        print("=" * 50)
        result = process_text_choices(test_case)
        print(f"最终结果: {result}")
        print()

if __name__ == "__main__":
    print("=== 测试正则表达式 ===")
    test_simple()
    
    print("\n=== 测试处理函数 ===")
    test_process_function()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/code')

from vng_unified_processor import VNGDataExtractor

def test_integrated_table_extraction():
    """测试集成后的表格提取功能"""
    
    # 创建提取器
    extractor = VNGDataExtractor(
        source_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified",
        output_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output",
        debug=True
    )
    
    # 测试包含表格的docx文件
    test_files = [
        "10528林厚桂.docx",  # 有Roll Test表格
        "488 颜佳祥.docx",   # 有Roll Test表格
        "380 黄美萍.docx"    # 有Roll Test表格
    ]
    
    for filename in test_files:
        # 查找文件
        test_file = None
        for root, dirs, files in os.walk("/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"):
            if filename in files:
                test_file = os.path.join(root, filename)
                break
        
        if not test_file:
            print(f"未找到文件: {filename}")
            continue
            
        print("=" * 80)
        print(f"测试文件: {filename}")
        print("=" * 80)
        
        # 提取文本（现在应该包含纵向表格处理）
        text = extractor.extract_text_from_file(test_file)
        print("\n提取的文本内容（前2000字符）:")
        print("-" * 40)
        print(text[:2000])
        print("-" * 40)
        
        # 提取患者数据
        patient_data = extractor.extract_patient_data(test_file)
        print("\n提取的位置试验数据:")
        print("-" * 40)
        print(f"位置试验: {patient_data.get('位置试验', '未找到')}")
        print("-" * 40)
        
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    print("开始测试集成后的表格提取功能...")
    test_integrated_table_extraction()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/code')

from vng_unified_processor import VNGDataExtractor

def test_single_file():
    """测试单个文件的提取效果"""
    
    # 创建提取器
    extractor = VNGDataExtractor(
        source_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified",
        output_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output",
        debug=True
    )
    
    # 测试文件
    test_file = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified/2022/6800施慧仑.wps"
    
    print("=" * 80)
    print(f"测试文件: {test_file}")
    print("=" * 80)
    
    # 提取文本
    text = extractor.extract_text_from_file(test_file)
    print("\n原始文本内容:")
    print("-" * 40)
    print(text[:2000])  # 显示前2000字符
    print("-" * 40)
    
    # 提取检查结果
    exam_results = extractor.extract_examination_results(text)
    print("\n提取的检查结果:")
    print("-" * 40)
    for key, value in exam_results.items():
        print(f"{key}: {value}")
    print("-" * 40)
    
    # 提取位置试验
    position_tests = extractor.extract_position_tests_combined(text)
    print(f"\n位置试验: {position_tests}")
    
    # 提取诊断
    diagnosis = extractor.extract_diagnosis(text)
    print(f"\n诊断信息:")
    for key, value in diagnosis.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    test_single_file()

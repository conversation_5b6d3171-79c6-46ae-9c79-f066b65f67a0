#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/code')

from vng_unified_processor import VNGDataExtractor

def test_nan_files():
    """测试包含nan的文件"""

    # 创建提取器
    extractor = VNGDataExtractor(
        source_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified",
        output_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output",
        debug=True
    )

    # 测试包含nan的文件 - 特别关注印象和摇头试验问题
    test_files = [
        "5415任春光.wps",  # 印象为nan
        "5424彭茹.wps",    # 印象为nan
        "6187李艺婵.wps"   # 摇头试验和印象可能混合
    ]

    for filename in test_files:
        # 查找文件
        import os
        test_file = None
        for root, dirs, files in os.walk("/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"):
            if filename in files:
                test_file = os.path.join(root, filename)
                break

        if not test_file:
            print(f"未找到文件: {filename}")
            continue

        print("=" * 80)
        print(f"测试文件: {filename}")
        print("=" * 80)

        # 提取文本
        text = extractor.extract_text_from_file(test_file)
        print("\n原始文本内容:")
        print("-" * 40)
        print(text[:1500])  # 显示前1500字符
        print("-" * 40)

        # 提取基本信息
        basic_info = extractor.extract_basic_info(text)
        print("\n提取的基本信息:")
        print("-" * 40)
        for key, value in basic_info.items():
            print(f"{key}: {value}")
        print("-" * 40)

        # 提取检查结果
        exam_results = extractor.extract_examination_results(text)
        print("\n提取的检查结果:")
        print("-" * 40)
        for key, value in exam_results.items():
            print(f"{key}: {value}")
        print("-" * 40)

        # 提取位置试验
        position_tests = extractor.extract_position_tests_combined(text)
        print(f"\n位置试验: {position_tests}")

        # 提取诊断
        diagnosis = extractor.extract_diagnosis(text)
        print(f"\n诊断信息:")
        for key, value in diagnosis.items():
            print(f"{key}: {value}")

        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    test_nan_files()

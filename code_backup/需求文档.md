# VNG病例信息提取系统需求文档

## 项目概述

基于现有的VNG病例分类系统，开发一个全面的病例信息提取工具，从已分类的VNG病例文档中提取完整的患者信息和检查数据，生成结构化的CSV数据库。

## 数据源分析

### 文件位置
- 源目录：`/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified`
- 文件结构：按年份分类（2018-2024年）+ undefined文件夹
- 文件格式：`.docx`、`.doc`、`.wps`
- 总计约6,876个有效VNG报告

### 病例文档结构分析

基于对现有病例文件的分析，VNG报告包含以下信息：

#### 1. 患者基本信息
- **姓名**：患者姓名
- **性别**：男/女
- **年龄**：患者年龄（岁）
- **检查日期**：VNG检查日期
- **科别**：就诊科室（如：神经一科门诊、神经一科23床等）
- **门诊/住院号**：患者就诊编号
- **编号**：检查编号

#### 2. VNG检查项目及结果
- **定标试验**：正常/异常
- **自发性眼震**：
  - 无/有
  - 水平向：左____%、右____%
  - 垂直向：上____%、下____%
- **凝视试验**：正常/异常
  - 定向
  - 变向
- **平滑跟踪**：Ⅰ型/Ⅱ型/Ⅲ型/Ⅳ型
- **扫视试验**：正常/异常（过冲/欠冲）
- **视动性眼震**：左右向视动反应（对称/不对称）

#### 3. 位置试验
- **Roll Test**：阴性/阳性
- **翻身试验**：阴性/阳性
- **变位试验 Dix-Hallpike**：
  - 左侧悬头位：阴性/阳性
  - 右侧悬头位：阴性/阳性
  - 疲劳现象：阴性/阳性

#### 4. 医生诊断
- **印象**：医生的诊断意见和建议
- **检查者**：执行检查的医生

## 功能需求

### 1. 核心功能
- **批量文档处理**：处理所有年份文件夹中的病例文档
- **多格式支持**：支持docx、doc、wps格式
- **智能信息提取**：使用正则表达式和文档结构分析提取信息
- **数据验证**：验证提取数据的完整性和准确性
- **CSV输出**：生成结构化的CSV数据文件

### 2. 错误处理
- **无法识别文件处理**：将无法正确提取信息的文件移动到专门文件夹
- **数据缺失标记**：对于缺失的字段进行标记
- **处理日志**：记录处理过程和错误信息

### 3. 输出格式

#### CSV文件结构
```csv
文件名,年份,姓名,性别,年龄,检查日期,科别,门诊住院号,编号,定标试验,自发性眼震,自发性眼震_水平向左,自发性眼震_水平向右,自发性眼震_垂直向上,自发性眼震_垂直向下,凝视试验,凝视试验_定向,凝视试验_变向,平滑跟踪,扫视试验,扫视试验_详情,视动性眼震,Roll_Test,翻身试验,Dix_Hallpike_左侧,Dix_Hallpike_右侧,疲劳现象,印象,检查者,原始文本
```

### 4. 特殊处理需求
- **复查病例**：识别并标记复查病例
- **异常格式**：处理格式不标准的病例文档
- **数据清洗**：清理多余空格、特殊字符等
- **重复数据**：识别可能的重复病例

## 技术实现方案

### 1. 基础架构
- 基于现有的`extract_patient_info.py`和`final_vng_classification.py`
- 使用python-docx处理docx文件
- 使用olefile处理doc/wps文件
- 正则表达式进行信息提取

### 2. 数据提取策略
- **分层提取**：先提取基本信息，再提取检查结果
- **模式匹配**：针对不同格式的文档使用不同的提取模式
- **容错处理**：对于格式变化的文档进行智能识别

### 3. 质量控制
- **数据完整性检查**：确保关键字段不为空
- **格式验证**：验证日期、数字等格式的正确性
- **逻辑验证**：检查数据的逻辑一致性

## 输出文件结构

```
DocExtraction/VNG/output/
├── vng_patient_data_YYYYMMDD_HHMMSS.csv     # 主要数据文件
├── processing_log_YYYYMMDD_HHMMSS.txt       # 处理日志
├── failed_files/                            # 处理失败的文件
│   ├── cannot_extract/                      # 无法提取信息的文件
│   └── format_error/                        # 格式错误的文件
└── statistics_report.txt                    # 统计报告
```

## 验收标准

1. **数据完整性**：成功提取率 > 90%
2. **数据准确性**：抽样验证准确率 > 95%
3. **处理效率**：平均处理速度 < 2秒/文件
4. **错误处理**：所有异常情况都有相应的处理机制
5. **可维护性**：代码结构清晰，易于扩展和维护

## 后续扩展

1. **数据分析功能**：基于提取的数据进行统计分析
2. **可视化界面**：开发GUI界面方便操作
3. **数据库集成**：将数据导入数据库系统
4. **报告生成**：自动生成统计报告和图表

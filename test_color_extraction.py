#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/code')

from vng_unified_processor import VNGDataExtractor

def test_color_extraction():
    """测试颜色选择提取功能"""
    
    # 创建提取器
    extractor = VNGDataExtractor(
        source_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified",
        output_dir="/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output",
        debug=True
    )
    
    # 测试一些docx文件（如果有的话）
    test_files = [
        "10528林厚桂.docx",  # docx文件
        "5360黎运容.wps",   # wps文件作为对比
        "5733苏敏聪.wps"    # wps文件作为对比
    ]
    
    for filename in test_files:
        # 查找文件
        test_file = None
        for root, dirs, files in os.walk("/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"):
            if filename in files:
                test_file = os.path.join(root, filename)
                break
        
        if not test_file:
            print(f"未找到文件: {filename}")
            continue
            
        print("=" * 80)
        print(f"测试文件: {filename}")
        print("=" * 80)
        
        # 提取文本
        text = extractor.extract_text_from_file(test_file)
        print("\n原始文本内容（前1000字符）:")
        print("-" * 40)
        print(text[:1000])
        print("-" * 40)
        
        # 如果是docx文件，尝试提取颜色信息
        if filename.endswith('.docx'):
            print("\n提取颜色选择信息:")
            print("-" * 40)
            colored_selections = extractor.extract_colored_selections_from_file(test_file)
            if colored_selections:
                for context, selected_option in colored_selections.items():
                    print(f"上下文: {context}")
                    print(f"选中选项: {selected_option}")
                    print()
            else:
                print("未找到颜色选择信息")
            print("-" * 40)
        
        # 提取检查结果
        colored_selections = None
        if filename.endswith('.docx'):
            colored_selections = extractor.extract_colored_selections_from_file(test_file)
        
        exam_results = extractor.extract_examination_results(text, colored_selections)
        print("\n提取的检查结果:")
        print("-" * 40)
        for key, value in exam_results.items():
            print(f"{key}: {value}")
        print("-" * 40)
        
        print("\n" + "="*80 + "\n")

def test_text_choice_processing():
    """测试文本选择处理功能"""

    extractor = VNGDataExtractor(
        "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified",
        "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output",
        False
    )
    
    # 测试用例
    test_cases = [
        "定标试验正常/异常",
        "自发性眼震无/有",
        "凝视试验正常/异常", 
        "视动性眼震对称/不对称",
        "Roll Test阴性/阳性",
        "平滑跟踪I型/II型/III型",
        "扫视试验正常"  # 没有选择项
    ]
    
    print("=" * 80)
    print("测试文本选择处理功能")
    print("=" * 80)
    
    for test_case in test_cases:
        processed = extractor.process_text_choices(test_case)
        print(f"原始: {test_case}")
        print(f"处理后: {processed}")
        print()

if __name__ == "__main__":
    print("开始测试颜色选择提取功能...")
    test_color_extraction()
    
    print("\n开始测试文本选择处理功能...")
    test_text_choice_processing()

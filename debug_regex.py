#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def test_regex():
    """测试正则表达式"""
    
    test_cases = [
        "定标试验正常/异常",
        "自发性眼震无/有",
        "凝视试验正常/异常", 
        "视动性眼震对称/不对称",
        "Roll Test阴性/阳性",
        "平滑跟踪I型/II型/III型",
        "扫视试验正常"  # 没有选择项
    ]
    
    patterns = [
        r'([^/\s]+/[^/\s]+(?:/[^/\s]+)*)',
        r'([^\s，。；：！？]+/[^\s，。；：！？]+(?:/[^\s，。；：！？]+)*)',
        r'([^/]+/[^/]+(?:/[^/]+)*)',
        r'(\w+/\w+(?:/\w+)*)',
        r'([^/\s，。；：！？]+/[^/\s，。；：！？]+)',
    ]
    
    for i, pattern in enumerate(patterns):
        print(f"\n=== 测试模式 {i+1}: {pattern} ===")
        for test_case in test_cases:
            matches = re.findall(pattern, test_case)
            print(f"输入: {test_case}")
            print(f"匹配: {matches}")
            print()

def test_apply_rules():
    """测试启发式规则"""
    
    def apply_heuristic_rules(choice_text):
        """应用启发式规则来处理选择项"""
        options = [opt.strip() for opt in choice_text.split('/')]
        if len(options) < 2:
            return choice_text
        
        # 一些常见的启发式规则
        # 规则1：如果包含"正常"和"异常"，优先选择"正常"（在没有颜色信息时）
        if '正常' in options and '异常' in options:
            return '正常'
        
        # 规则2：如果包含"无"和"有"，优先选择"无"
        if '无' in options and '有' in options:
            return '无'
        
        # 规则3：如果包含"阴性"和"阳性"，优先选择"阴性"
        if '阴性' in options and '阳性' in options:
            return '阴性'
        
        # 规则4：如果包含"对称"和"不对称"，优先选择"对称"
        if '对称' in options and '不对称' in options:
            return '对称'
        
        # 其他情况保持原样
        return choice_text
    
    test_choices = [
        "正常/异常",
        "无/有",
        "阴性/阳性",
        "对称/不对称",
        "I型/II型/III型"
    ]
    
    print("\n=== 测试启发式规则 ===")
    for choice in test_choices:
        result = apply_heuristic_rules(choice)
        print(f"输入: {choice} -> 输出: {result}")

if __name__ == "__main__":
    test_regex()
    test_apply_rules()
